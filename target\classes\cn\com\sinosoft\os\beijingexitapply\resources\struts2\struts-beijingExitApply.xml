<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE struts PUBLIC
	"-//Apache Software Foundation//DTD Struts Configuration 2.0//EN"
	"http://struts.apache.org/dtds/struts-2.0.dtd">
<struts>
	<package name="beijingExitApply" extends="bsp-default" namespace="/cn/com/sinosoft/os/beijingexitapply">
		<!-- 锟斤拷询 -->
		<action name="qryBeijingExitApply" class="beijingExitApplyAction" method="qryParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitapply/qryBeijingExitApply.jsp
			</result>
		</action>
		<action name="qryBeijingExitApplyList" class="commonQueryAction">
			<param name="sessionGroup">beijingExitApply</param>
			<param name="queryCode">QRY_OS_BEIJING_EXIT_APPLY</param>
			<param name="resultName">qryList</param>
		</action>
		<!-- 锟介看 -->
		<action name="beijingExitApply_viewParent" class="beijingExitApplyAction" method="viewParent">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		<!-- 锟斤拷锟� -->
		<action name="beijingExitApply_addParentInput" class="beijingExitApplyAction" method="addParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		<action name="beijingExitApply_addParentSubmit" class="beijingExitApplyAction" method="addParentSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		<!-- 锟睫革拷 -->
		<action name="beijingExitApply_edtParentInput" class="beijingExitApplyAction" method="edtParentInput">
			<param name="sessionGroup">beijingExitApply</param>
			<result name="success">
				/WEB-INF/pages/cn/com/sinosoft/os/beijingexitapply/edtBeijingExitApply.jsp
			</result>
		</action>
		<action name="beijingExitApply_edtParentSubmit" class="beijingExitApplyAction" method="edtParentSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
		<!-- 删锟斤拷 -->
		<action name="beijingExitApply_delParentSubmit" class="beijingExitApplyAction" method="delParentSubmit">
			<param name="sessionGroup">beijingExitApply</param>
		</action>
	</package>
</struts>