package cn.com.sinosoft.os.beijingexitapply.service;

import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import java.util.List;

/**
 * 出京申请 - service接口.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public interface BeijingExitApplyService {

	/**
	 * 出京申请 - 获取单个对象.
	 *
	 * <AUTHOR>
	 * @param id
	 *			     主键
	 * @return 出京申请 对象
	 */
	BeijingExitApply get(String id);

	/**
	 * 出京申请 - 删除.
	 *
	 * <AUTHOR>
	 * @param id
	 *			     主键
	 */
	void delete(String id);

	/**
	 * 出京申请 - 保存.
	 *
	 * <AUTHOR>
	 * @param result
	 *			     出京申请 对象
	 */
	void save(BeijingExitApply result);

	/**
	 * 出京申请 - 修改.
	 *
	 * <AUTHOR>
	 * @param result
	 *			     出京申请 对象
	 */
	void edit(BeijingExitApply result);

}