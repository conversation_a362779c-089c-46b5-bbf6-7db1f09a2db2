package cn.com.sinosoft.os.beijingexitapply.service.impl;

import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import ie.bsp.frame.dao.CommonBaseDao;
import ie.bsp.core.bean.UserView;
import java.util.List;
import java.util.Date;
import org.apache.struts2.ServletActionContext;
import ie.bsp.ui.FrameConstant;
import ie.weaf.toolkit.Util;
import java.util.ArrayList;
import java.util.HashMap;

/**
 * 出京申请 - service接口实现.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public class BeijingExitApplyServiceImpl implements BeijingExitApplyService {

	// 通用dao.
	private CommonBaseDao dao;

	/**
	 * 获取 通用dao.
	 *
	 * @return 通用dao
	 */
	public CommonBaseDao getDao() {
		return dao;
	}

	/**
	 * 设置 通用dao.
	 *
	 * @param dao
	 *			     通用dao
	 */
	public void setDao(CommonBaseDao dao) {
		this.dao = dao;
	}

	@Override
	public BeijingExitApply get(String id) {
		return (BeijingExitApply) dao.get(BeijingExitApply.class,id);
	}

	@Override
	public void delete(String ids) {
		String[] arrId = ids.split(",");

		for (int i = 0; i < arrId.length; i++) {
			dao.excuteSql("update OS_BEIJING_EXIT_APPLY set STATE='0' where 1=1 "
					+ " and ID = '" + arrId[i] + "'");
		}
	}

	@Override
	public void save(BeijingExitApply result) {
		UserView user = (UserView) ServletActionContext.getRequest().getSession().
				getAttribute(FrameConstant.SESSION_USERVIEW);
		result.setAddZone(user.getZonecode());
		result.setAddOrg(user.getOrgcode());
		result.setAddDep(user.getDepartmentId());
		result.setAddUser(user.getUsername());
		result.setAddTime(new Date());
		result.setModyZone(user.getZonecode());
		result.setModyOrg(user.getOrgcode());
		result.setModyDep(user.getDepartmentId());
		result.setModyUser(user.getUsername());
		result.setModyTime(new Date());
		result.setState("1");
		result.setId(ie.bsp.util.UUID.randomUUID().toString());
		dao.save(result);
	}

	@Override
	public void edit(BeijingExitApply result) {
		UserView user = (UserView) ServletActionContext.getRequest().getSession()
				.getAttribute(FrameConstant.SESSION_USERVIEW);
		result.setModyZone(user.getZonecode());
		result.setModyOrg(user.getOrgcode());
		result.setModyDep(user.getDepartmentId());
		result.setModyUser(user.getUsername());
		result.setModyTime(new Date());
		dao.edit(result);
	}

}