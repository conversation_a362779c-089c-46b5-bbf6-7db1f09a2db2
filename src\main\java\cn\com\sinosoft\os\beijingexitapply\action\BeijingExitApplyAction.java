package cn.com.sinosoft.os.beijingexitapply.action;

import ie.bsp.frame.action.BaseEditAction;
import ie.bsp.frame.exception.GeneralException;
import cn.com.sinosoft.os.beijingexitapply.model.BeijingExitApply;
import cn.com.sinosoft.os.beijingexitapply.service.BeijingExitApplyService;
import ie.weaf.toolkit.Util;
import ie.bsp.frame.exception.GeneralExceptionHandler;

/**
 * ???????? - Action.
 *
 * <AUTHOR>
 * @date: 2025/08/02 15:41:43
 * @version V1.0
 */
public class BeijingExitApplyAction extends BaseEditAction {

	/**
	 * ??????.
	 */
	public BeijingExitApplyAction() {
		moduleId = "";
	}

	// serialVersionUID.
	private static final long serialVersionUID = 1L;

	// ???????? - ???.
	private BeijingExitApplyService service;

	// ???????? - ????.
	private BeijingExitApply result;

	// ????.
	private String id;

	/**
	 * ??? ???????? - ???.
	 *
	 * @return ???????? - ???
	 */
	public BeijingExitApplyService getService() {
		return service;
	}

	/**
	 * ???? ???????? - ???.
	 *
	 * @param service
	 *			     ???????? - ???
	 */
	public void setService(BeijingExitApplyService service) {
		this.service = service;
	}

	/**
	 * ??? ???????? - ????.
	 *
	 * @return ???????? - ????
	 */
	public BeijingExitApply getResult() {
		return result;
	}

	/**
	 * ???? ???????? - ????.
	 *
	 * @param result
	 *			     ???????? - ????
	 */
	public void setResult(BeijingExitApply result) {
		this.result = result;
	}

	/**
	 * ??? ????.
	 *
	 * @return ????
	 */
	public String getId() {
		return id;
	}

	/**
	 * ???? ????.
	 *
	 * @param id
	 *			     ????
	 */
	public void setId(String id) {
		this.id = id;
	}

	@Override
	public void doQryParentInput() throws GeneralException {
		
	}

	@Override
	public void doViewParentSubmit() throws GeneralException {
		funcId = "";
		result = service.get(id);
	}

	@Override
	public void doDelteParentSubmit() throws GeneralException {
		funcId = "";
		service.delete(id);
	}

	@Override
	public void doAddParentInput() throws GeneralException {
		
	}

	@Override
	public void doAddParentSubmit() throws GeneralException {
		funcId = "";
		service.save(result);
	}

	@Override
	public void doEditParentInput() throws GeneralException {
		doViewParentSubmit();
	}

	@Override
	public void doEditParentSubmit() throws GeneralException {
		funcId = "";
		service.edit(result);
	}

	@Override
	public void doAudit() throws GeneralException {
		funcId = "";
	}

}